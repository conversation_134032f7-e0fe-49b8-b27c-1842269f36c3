.collapsible-text {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: #fafafa;
  overflow: hidden;
}

.collapsible-text-empty {
  padding: 12px;
  text-align: center;
  color: #999;
  font-style: italic;
}

.collapsible-text-placeholder {
  color: #999;
  font-style: italic;
}

.collapsible-text-header {
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  padding: 8px 12px;
}

.collapsible-text-toggle {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
  padding: 0;
  width: 100%;
  text-align: left;
}

.collapsible-text-toggle:hover {
  color: #333;
}

.collapsible-text-arrow {
  display: inline-block;
  transition: transform 0.2s ease;
  font-size: 10px;
  color: #999;
}

.collapsible-text-arrow.expanded {
  transform: rotate(90deg);
}

.collapsible-text-arrow.collapsed {
  transform: rotate(0deg);
}

.collapsible-text-status {
  font-weight: 500;
}

.collapsible-text-content-wrapper {
  position: relative;
  overflow: hidden;
}

.collapsible-text-content-wrapper.collapsed {
  max-height: 120px; /* 大约3-4行的高度 */
}

.collapsible-text-content-wrapper.expanded {
  max-height: none;
}

.collapsible-text-content {
  margin: 0;
  padding: 12px;
  font-family: 'Courier New', Consolas, monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  background: #fff;
  color: #333;
  border: none;
  overflow-x: auto;
}

.collapsible-text-fade {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: linear-gradient(transparent, rgba(255, 255, 255, 0.9));
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 8px;
}

.collapsible-text-more {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 4px 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.collapsible-text-more-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 11px;
  color: #007bff;
  padding: 0;
  text-decoration: underline;
}

.collapsible-text-more-btn:hover {
  color: #0056b3;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .collapsible-text-content {
    font-size: 11px;
    padding: 8px;
  }
  
  .collapsible-text-header {
    padding: 6px 8px;
  }
  
  .collapsible-text-toggle {
    font-size: 11px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .collapsible-text {
    border-color: #000;
  }
  
  .collapsible-text-header {
    background: #f0f0f0;
    border-bottom-color: #000;
  }
  
  .collapsible-text-content {
    background: #fff;
    color: #000;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .collapsible-text {
    background: #2d2d2d;
    border-color: #555;
  }
  
  .collapsible-text-header {
    background: #3d3d3d;
    border-bottom-color: #555;
  }
  
  .collapsible-text-toggle {
    color: #ccc;
  }
  
  .collapsible-text-toggle:hover {
    color: #fff;
  }
  
  .collapsible-text-content {
    background: #2d2d2d;
    color: #e0e0e0;
  }
  
  .collapsible-text-placeholder {
    color: #888;
  }
  
  .collapsible-text-fade {
    background: linear-gradient(transparent, rgba(45, 45, 45, 0.9));
  }
  
  .collapsible-text-more {
    background: rgba(45, 45, 45, 0.95);
  }
  
  .collapsible-text-more-btn {
    color: #66b3ff;
  }
  
  .collapsible-text-more-btn:hover {
    color: #99ccff;
  }
}
