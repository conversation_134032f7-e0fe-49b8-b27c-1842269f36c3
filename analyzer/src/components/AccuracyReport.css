.accuracy-report {
  padding: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.accuracy-status {
  margin-bottom: 8px;
}

.accuracy-value {
  margin-bottom: 8px;
}

.accuracy-timeout {
  margin-bottom: 8px;
}

.missed-cells {
  margin-top: 12px;
}

.missed-detail-table {
  margin-top: 8px;
  /* 移除固定高度和滚动条设置 */
  border: 1px solid #ddd;
  border-radius: 4px;
  /* 添加自动高度和最小宽度 */
  display: inline-block;
  min-width: 100%;
}

.missed-detail-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
  /* 确保表格布局适应内容 */
  table-layout: auto;
}

.missed-detail-table th,
.missed-detail-table td {
  padding: 4px 8px;
  border: 1px solid #ddd;
  text-align: left;
  vertical-align: top;
  /* 防止内容换行 */
  white-space: nowrap;
}

.missed-detail-table th {
  background-color: #f8f9fa;
  font-weight: bold;
  /* 移除 sticky 属性 */
}

.missed-detail-table td {
  /* 移除最大宽度限制 */
  word-break: normal;
  /* 添加文本溢出处理 */
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px; /* 可调整为您需要的最大值 */
}

.missed-detail-table tbody tr:nth-child(even) {
  background-color: #f9f9f9;
}

.missed-detail-table tbody tr:hover {
  background-color: #e9ecef;
}