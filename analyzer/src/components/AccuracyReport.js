import React, { useMemo } from 'react';
import { parseTableContent, getDiffCells } from '../utils/dataProcessor';
import './AccuracyReport.css';

/**
 * 准确率报告组件
 * 显示解析结果的准确率和未命中的单元格详情
 */
const AccuracyReport = ({ 
  baselineText, 
  actualText, 
  accuracy, 
  parserName,
  hasTable = true,
  isTimeout = false,
  processingTime = null
}) => {
  const diffCells = useMemo(() => {
    if (!baselineText || !actualText || accuracy >= 100) {
      return [];
    }

    try {
      const baselineTable = parseTableContent(String(baselineText));
      const actualTable = parseTableContent(String(actualText));

      if (baselineTable && actualTable) {
        return getDiffCells(baselineTable, actualTable);
      }
    } catch (error) {
      console.warn(`计算未命中单元格失败 (${parserName}):`, error);
    }

    return [];
  }, [baselineText, actualText, accuracy, parserName]);

  // 处理超时情况
  if (isTimeout) {
    return (
      <div className="accuracy-report">
        <div className="accuracy-status">
          <b>是否解析表格：</b>
          <span style={{ color: 'red', fontWeight: 'bold' }}>✗ 否（超时）</span>
        </div>
        <div className="accuracy-timeout">
          <span style={{ color: 'red' }}>
            ⏰ 超时 ({processingTime ? processingTime.toFixed(1) : '未知'}秒)
          </span>
        </div>
      </div>
    );
  }

  // 处理无结果情况
  if (!actualText || actualText.trim() === '') {
    return (
      <div className="accuracy-report">
        <div className="accuracy-status">
          <b>是否解析表格：</b>
          <span style={{ color: 'red', fontWeight: 'bold' }}>✗ 否（无结果）</span>
        </div>
        <div className="accuracy-value">
          <b>准确率：</b>-
        </div>
      </div>
    );
  }

  const tableStatus = hasTable ? '✓ 是' : '✗ 否';
  const statusColor = hasTable ? 'green' : 'red';

  return (
    <div className="accuracy-report">
      <div className="accuracy-status">
        <b>是否解析表格：</b>
        <span style={{ color: statusColor, fontWeight: 'bold' }}>
          {tableStatus}
        </span>
      </div>
      
      <div className="accuracy-value">
        <b>准确率：</b>
        {accuracy !== null ? `${accuracy.toFixed(1)}%` : '-'}
      </div>

      {diffCells.length > 0 && (
        <div className="missed-cells">
          <b>未命中单元格：</b>
          <div className="missed-detail-table">
            <table>
              <thead>
                <tr>
                  <th>行</th>
                  <th>列</th>
                  <th>内容</th>
                </tr>
              </thead>
              <tbody>
                {diffCells
                  .slice()
                  .sort((a, b) => {
                    // 先按行排序
                    const rowCompare = Number(a.row) - Number(b.row);
                    // 行相同则按列排序
                    return rowCompare !== 0 ? rowCompare : Number(a.col) - Number(b.col);
                  })
                  .map((cell, index) => (
                    <tr key={index}>
                      <td>{cell.row}</td>
                      <td>{cell.col}</td>
                      <td>{cell.content}</td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccuracyReport;
